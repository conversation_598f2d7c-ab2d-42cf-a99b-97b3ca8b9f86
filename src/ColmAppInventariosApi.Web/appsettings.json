{"ConnectionStrings": {"DefaultConnection": "Host=ep-bitter-salad-a8y8lihx-pooler.eastus2.azure.neon.tech;Port=5432;Database=colmapp-inventarios;Username=colmapp-inventarios_owner;Password=****************;SSL Mode=Require;"}, "CorsOrigins": ["http://localhost:5173"], "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/log_.txt", "rollingInterval": "Day", "retainedFileCountLimit": 7, "fileSizeLimitBytes": 10485760, "rollOnFileSizeLimit": true, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}